from llm_manager.openai_llm import OpenAILLM
from llm_manager.anthropic_llm import AnthropicLLM
from llm_manager.huggingface_llm import HuggingFaceLLM
from llm_manager.perplexity_llm import PerplexityLLM

from langchain_core.prompts import ChatPromptTemplate, MessagesPlaceholder


class LLMFactory:
    def __init__(self, config):
        """
        Initialize with a configuration dictionary.
        """
        self.config = config

    def get_llm(self, llm_name, llm_model, req_timeout=600):
        if llm_name == "openai":
            return OpenAILLM(**self.config["openai"]).get_llm(llm_model, req_timeout)
        elif llm_name == "openai_admin":
            return OpenAILLM(**self.config["openai_admin"]).get_llm(llm_model, req_timeout)
        elif llm_name == "openai_extraction":
            return OpenAILLM(**self.config["openai_extraction"]).get_llm(llm_model, req_timeout)
        elif llm_name == "openai_drive":
            return OpenAILLM(**self.config["openai_drive"]).get_llm(llm_model, req_timeout)
        elif llm_name == "anthropic":
            return AnthropicLLM(**self.config["anthropic"]).get_llm(llm_model)
        elif llm_name == "huggingface":
            return HuggingFaceLLM(**self.config["huggingface"]).get_llm(llm_model)
        elif llm_name == "perplexity":
            print(llm_name)
            return PerplexityLLM(**self.config["perplexity"]).get_llm(llm_model, req_timeout)
        else:
            raise ValueError(f"Unknown LLM name: {llm_name}")

    @staticmethod
    def create_qa_prompt(system_prompt, chat_history_placeholder="chat_history"):
        """
        Create a reusable ChatPromptTemplate.
        :param system_prompt: The system prompt to use.
        :param chat_history_placeholder: Placeholder name for chat history.
        :return: A ChatPromptTemplate instance.
        """
        return ChatPromptTemplate.from_messages(
            [
                ("system", system_prompt),
                MessagesPlaceholder(chat_history_placeholder),
                ("human", "{input}"),
            ]
        )


