# api.py

import logging
import os
import uuid
import traceback
import time

from fastapi import HTTPException, APIRouter, UploadFile, File, Form, Depends
from starlette.requests import Request
from fastapi.responses import HTMLResponse
from fastapi.templating import Jinja2Templates
from pdf2image import convert_from_bytes
from auth.dependencies import require_login, getuser
from auth.rbac import require_roles

import config
from llm_manager.llm_factory import LLMFactory
from config import llm_config
from agents.mcq_extractor import MCQExtractor
from agents.toc_extractor import TOCExtractor
from app.pdf_processor import process_pdf, process_pdf_new, text_processor, process_ext_content
from app.data_retriever import (generate_response, generate_response_admin_new,
                                generate_ext_response, check_namespace_in_redis, generate_response_md, generate_mcq_res,)
from models.api_models import PDFTextExtractQuery, PDFTextExtractByChapterQuery, \
    ChatImageRequest, MCQExtractQuery, TOCExtractByResourceQuery, \
    MCQTextExtractQuery, TaskStatusRequest, PromptRequest
from models.api_models import AdminQuery, DeleteNs, AdminQueryNew, ProcessPDFQuery, DeleteNamespaceRequest
from config import INDEX_NAME_USER, INDEX_NAME_ADMIN, INDEX_NAME_DRIVE
from utils.file_cleanup import cleanup_temp_files
from utils.html_builder import build_final_html
from utils.extraction_storage import save_extraction_result, generate_unique_id
from utils.enhanced_validator import EnhancedValidator
from utils.openai_vision import extract_html_from_image
from utils.pinecone_utils import get_namespaces, delete_old_ns, delete_namespace

from app.tts_processor import process_tts
from fastapi.responses import JSONResponse
import base64
import json
from services.pattern_extraction_service import create_solution_with_sample
from openai import OpenAI

# Initialize the API router
router = APIRouter()

# Get logger instance
logger = logging.getLogger(__name__)

# Initialize LLM factory
llm_factory = LLMFactory(llm_config)

templates = Jinja2Templates(directory="web/templates")

UPLOAD_DIR = "storage/uploads"
IMAGES_DIR = "storage/page_images"
OUTPUT_HTML_DIR = "storage/output_html"
PAGE_HTMLS_DIR = "storage/page_htmls"
EXTRACTED_TEXT_DIR = "storage/extracted_text"
MCQ_EXTRACTION_DIR = "storage/mcq_extraction"
TOC_EXTRACTION_DIR = "storage/toc_extraction"

os.makedirs(UPLOAD_DIR, exist_ok=True)
os.makedirs(IMAGES_DIR, exist_ok=True)
os.makedirs(PAGE_HTMLS_DIR, exist_ok=True)
os.makedirs(OUTPUT_HTML_DIR, exist_ok=True)
os.makedirs(EXTRACTED_TEXT_DIR, exist_ok=True)
os.makedirs(MCQ_EXTRACTION_DIR, exist_ok=True)
os.makedirs(TOC_EXTRACTION_DIR, exist_ok=True)


# Root route is now handled by login_api.py

@router.get("/test", response_class=HTMLResponse)
def test_page(request: Request):
    return templates.TemplateResponse("test.html", {"request": request})

@router.get("/preview/{filename}", response_class=HTMLResponse)
async def preview_merged_html(filename: str):
    file_path = os.path.join(OUTPUT_HTML_DIR, filename)

    # Basic security check: prevent directory traversal
    if not file_path.endswith(".html") or ".." in filename:
        raise HTTPException(status_code=400, detail="Invalid filename.")

    if not os.path.exists(file_path):
        raise HTTPException(status_code=404, detail="File not found.")

    with open(file_path, "r", encoding="utf-8") as f:
        content = f.read()

    return HTMLResponse(content=content)


@router.get("/home", response_class=HTMLResponse)
def home_page(request: Request, login_check=Depends(require_login)):
    if login_check:
        return login_check
    return templates.TemplateResponse("home.html", {"request": request})

@router.get("/mcq-text-extractor", response_class=HTMLResponse)
def mcq_text_extractor_page(request: Request, login_check=Depends(require_login), role_check=Depends(require_roles(["ROLE_GPT_MANAGER"]))):
    """MCQ Text Extractor page"""
    if login_check:
        return login_check
    return templates.TemplateResponse("mcq_text_extractor.html", {"request": request})

@router.get("/chat-completion-test", response_class=HTMLResponse)
def chat_completion_test_page(request: Request, login_check=Depends(require_login)):
    """Chat Completion Test page"""
    if login_check:
        return login_check
    return templates.TemplateResponse("chat_completion_test.html", {"request": request})


@router.get("/toc-extractor", response_class=HTMLResponse)
def toc_extractor_page(request: Request, login_check=Depends(require_login), role_check=Depends(require_roles(["ROLE_GPT_MANAGER"]))):
    """TOC Extractor page"""
    if login_check:
        return login_check
    return templates.TemplateResponse("toc_extractor.html", {"request": request})


@router.get("/api/mcq-content")
async def get_mcq_content(file_path: str, login_check=Depends(require_login), role_check=Depends(require_roles(["ROLE_GPT_MANAGER"]))):
    """Get the content of an MCQ extraction file"""
    if login_check:
        return login_check

    # Log the request
    logger.info(f"Getting MCQ content from file: {file_path}")

    try:
        # Check if the file exists
        if not os.path.exists(file_path):
            logger.error(f"File not found: {file_path}")
            raise HTTPException(status_code=404, detail=f"File not found: {file_path}")

        # Read the file content
        with open(file_path, "r", encoding="utf-8") as f:
            content = f.read()

        # Return the content
        return {"content": content}
    except HTTPException:
        # Re-raise HTTP exceptions
        raise
    except Exception as e:
        # Log the error
        logger.error(f"Error reading MCQ content from file: {str(e)}")
        logger.error(traceback.format_exc())
        raise HTTPException(status_code=500, detail=f"Error reading file: {str(e)}")


@router.get("/api/mcq-s3-content")
async def get_mcq_s3_content(s3_path: str, login_check=Depends(require_login), role_check=Depends(require_roles(["ROLE_GPT_MANAGER"]))):
    """Get the content of an MCQ extraction file from S3"""
    if login_check:
        return login_check

    try:
        # Import S3 utilities
        from utils.s3_utils import read_file_from_s3, get_s3_path
        full_s3_path = get_s3_path(s3_path)
        content_bytes = read_file_from_s3(full_s3_path)

        if content_bytes is None:
            logger.error(f"File not found in S3: {s3_path}")
            raise HTTPException(status_code=404, detail=f"File not found in S3: {s3_path}")

        # Convert bytes to string
        content = content_bytes.decode('utf-8')

        # Return the content
        return {"content": content, "base_url": config.BASE_URL}
    except HTTPException:
        # Re-raise HTTP exceptions
        raise
    except Exception as e:
        # Log the error
        logger.error(f"Error reading MCQ content from S3: {str(e)}")
        logger.error(traceback.format_exc())

        # Return error response
        raise HTTPException(status_code=500, detail=f"Error reading file from S3: {str(e)}")


@router.get("/api/extract-mcq/status/{res_id}")
async def check_mcq_extraction_status(res_id: str, login_check=Depends(require_login), role_check=Depends(require_roles(["ROLE_GPT_MANAGER"]))):
    """Check the status of MCQ extraction for a resource"""
    if login_check:
        return login_check
    try:
        # Create an MCQExtractor instance
        mcq_extractor = MCQExtractor()
        result = await mcq_extractor.check_resource_processed(res_id)
        # Return the result
        return result

    except Exception as e:
        # Log the error
        logger.error(f"Error checking MCQ extraction status for resource ID {res_id}: {str(e)}")
        logger.error(traceback.format_exc())
        # Return error response
        return {"status": "error", "message": str(e)}


@router.get("/api/validate-mcq/{res_id}")
async def validate_mcq(res_id: str, login_check=Depends(require_login), role_check=Depends(require_roles(["ROLE_GPT_MANAGER"]))):
    """Validate MCQs for a given resource ID.

    Args:
        res_id: Resource ID

    Returns:
        Dict: Validation results
    """
    if login_check:
        return login_check

    try:
        # Get a database session for the wscontent schema
        from db_config.db import get_session, CONTENT_SCHEMA
        from sqlalchemy import text
        import traceback

        # Get resource details from database
        quiz_id = None
        db_session = next(get_session(CONTENT_SCHEMA))
        try:
            # Query the resource_dtl table to get resource details
            resource_query = text("""
                SELECT id, chapter_id, res_link, resource_name
                FROM wscontent.resource_dtl
                WHERE id = :res_id AND res_type = 'Multiple Choice Questions'
                LIMIT 1
            """)

            resource_result = db_session.execute(resource_query, {"res_id": res_id})
            resource_row = resource_result.fetchone()

            if not resource_row:
                logger.warning(f"No MCQ resource found with ID: {res_id}")
                return {"status": "error", "message": f"No MCQ resource found with ID {res_id}"}

            # Extract resource details
            resource_id = resource_row[0]
            chapter_id = resource_row[1]
            quiz_id = resource_row[2]  # res_link contains the quiz_id
        finally:
            # Close the database session immediately after retrieving data
            db_session.close()

        # Now that we have the quiz_id, validate the MCQs
        if quiz_id:
            validator = MCQValidator()
            validation_results = await validator.validate_mcqs(quiz_id)
            return validation_results
        else:
            return {"status": "error", "message": "Failed to retrieve quiz ID from resource"}
    except Exception as e:
        logger.error(f"Error validating MCQs: {e}")
        logger.error(traceback.format_exc())
        return {"status": "error", "message": f"Error validating MCQs: {str(e)}"}

@router.post("/api/extract-toc")
async def extract_toc(
    pdfFile: UploadFile = File(...),
    login_check=Depends(require_login),
    role_check=Depends(require_roles(["ROLE_GPT_MANAGER"]))
):
    """Extract table of contents from a PDF file

    This endpoint accepts a PDF file, converts it to images, and extracts the table of contents
    using the TOC extractor agent.

    Args:
        pdfFile: The PDF file to extract TOC from

    Returns:
        Dict: Extraction results with TOC entries
    """
    if login_check:
        return login_check

    try:
        # Generate a unique filename for the uploaded PDF
        original_filename = pdfFile.filename
        extraction_id = str(uuid.uuid4())
        unique_filename = f"{extraction_id}_{original_filename}"

        # Make sure the directory exists
        os.makedirs(config.PDF_PAGE_IMG_OUTPUT_DIR, exist_ok=True)
        pdf_path = os.path.join(config.PDF_PAGE_IMG_OUTPUT_DIR, unique_filename)

        # Save the uploaded PDF
        with open(pdf_path, "wb") as f:
            f.write(await pdfFile.read())

        logger.info(f"PDF file saved to {pdf_path}")

        # Create a TOC extractor instance
        toc_extractor = TOCExtractor()

        # Process the PDF and extract TOC
        result = await toc_extractor.process_pdf_and_extract_toc(pdf_path, extraction_id)

        # Clean up the uploaded file
        try:
            os.remove(pdf_path)
            logger.info(f"Cleaned up temporary PDF file: {pdf_path}")
        except Exception as e:
            logger.warning(f"Failed to clean up temporary PDF file: {e}")

        return result

    except Exception as e:
        logger.error(f"Error extracting TOC: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return {"status": "error", "message": str(e)}


@router.post("/api/extract-toc-by-resource")
async def extract_toc_by_resource(extract_query: TOCExtractByResourceQuery):
    """Extract table of contents from a PDF by S3 path

    This endpoint accepts an S3 path to a PDF file and extracts the table of contents
    using the TOC extractor agent.

    Args:
        extract_query: TOCExtractByResourceQuery containing the S3 path, TOC page numbers, and starting page number

    Returns:
        Dict: Extraction results with TOC entries
    """

    # Get the parameters from the query
    s3_path = extract_query.s3_path
    toc_page_numbers = extract_query.toc_page_numbers
    starting_page_number = extract_query.starting_page_number

    # Log the parameters
    logger.info(f"Extracting TOC from S3 path: {s3_path}")
    if toc_page_numbers:
        logger.info(f"Using specific TOC page numbers: {toc_page_numbers}")
    if starting_page_number:
        logger.info(f"Using starting page number: {starting_page_number}")

    try:
        # Create a TOC extractor instance
        toc_extractor = TOCExtractor()

        # Process the extraction with the new parameters
        result = await toc_extractor.process_toc_extraction_by_s3_path(
            s3_path,
            toc_page_numbers=toc_page_numbers,
            starting_page_number=starting_page_number
        )
        return result

    except Exception as e:
        logger.error(f"Error extracting TOC for S3 path {s3_path}: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return {"status": "error", "message": str(e)}


@router.post("/api/extract-mcq")
async def extract_mcq(request: MCQExtractQuery, login_check=Depends(require_login), username=Depends(getuser), role_check=Depends(require_roles(["ROLE_GPT_MANAGER"]))):
    """Extract MCQs from a PDF resource"""
    if login_check:
        return login_check

    # Get the resource ID
    res_id = request.res_id

    # Log the start of extraction with a unique identifier
    request_id = str(uuid.uuid4())
    logger.info(f"[REQUEST:{request_id}] Starting MCQ extraction for resource ID {res_id}")

    try:
        mcq_extractor = MCQExtractor(max_concurrent_extractions=3)
        status_result = await mcq_extractor.check_resource_processed(res_id)

        if status_result['status'] == 'processed' and status_result.get('file_count', 0) > 0:
            logger.info(f"[REQUEST:{request_id}] Resource already processed with {status_result['file_count']} files")
            # We'll still proceed with the normal extraction to get structured results
            logger.info(f"[REQUEST:{request_id}] Proceeding with extraction to get structured results")

        import asyncio
        start_time = time.time()

        # Use a timeout to ensure we don't wait forever
        try:
            # Try to run the extraction with a timeout
            result = await asyncio.wait_for(
                mcq_extractor.extract_mcqs_from_resource(res_id=res_id, username=username),
                timeout=1800  # 30 minutes timeout
            )
        except asyncio.TimeoutError:
            # Check again if files were created during the timeout
            status_result = await mcq_extractor.check_resource_processed(res_id)

            if status_result['status'] == 'processed' and status_result.get('file_count', 0) > 0:
                # Create a basic response with the files we have
                result = {
                    "status": "partial_success",
                    "message": f"Extraction timed out but found {status_result['file_count']} processed files",
                    "file_count": status_result['file_count'],
                    "output_dir": status_result['output_dir'],
                    "files": status_result['files']
                }
            else:
                result = {
                    "status": "error",
                    "message": "Extraction timed out after 30 minutes and no files were created"
                }

        end_time = time.time()

        # Calculate total processing time
        total_time = end_time - start_time

        # Add request tracking information to the result
        result["request_id"] = request_id
        result["total_processing_time"] = total_time

        return result
    except Exception as e:
        # Log the error with detailed information
        logger.error(f"[REQUEST:{request_id}] Error in MCQ extraction for resource ID {res_id}: {str(e)}")
        logger.error(traceback.format_exc())

        # Return error response with request tracking information
        return {
            "status": "error",
            "message": str(e),
            "request_id": request_id
        }


async def process_mcq_text_extraction_background(request_task_id: str, mcq_task_id: str, res_id: str, username: str,
                                                force_reextract: bool, total_questions: int,
                                                explanation_start_page: int = None):
    """
    Background task function for MCQ text extraction.

    Args:
        request_task_id: Request tracker task ID for tracking
        mcq_task_id: MCQ extract log task ID for logging
        res_id: Resource ID
        username: Username
        force_reextract: Whether to force re-extraction
        total_questions: Total number of questions
        explanation_start_page: Page number from which explanations start (None to skip explanation extraction)
    """
    # Import services inside the background task to avoid blocking main thread
    from services.request_tracker_service import RequestTrackerService
    from agents.mcq_extract_log import MCQExtractLog

    # Initialize services first (lightweight)
    tracker_service = RequestTrackerService()
    mcq_extract_log = MCQExtractLog()

    try:
        # Update both tracking systems to IN_PROGRESS immediately
        tracker_service.update_task_status(request_task_id, 'IN_PROGRESS')
        # Note: MCQ extract log doesn't have IN_PROGRESS status, it stays as 'inprogress' until completion

        logger.info(f"[TASK:{request_task_id}] Starting background MCQ text extraction for resource ID {res_id}")
        logger.info(f"[MCQ_TASK:{mcq_task_id}] MCQ extract log created for resource ID {res_id}")

        # Add a small delay to ensure the API response is sent first
        import asyncio
        await asyncio.sleep(0.1)

        # Now import and initialize the heavy service
        from services.mcq_text_extractor_service import MCQTextExtractorService
        extractor_service = MCQTextExtractorService()

        logger.info(f"[TASK:{request_task_id}] MCQ text extractor service initialized")

        # Call the service to extract text
        result = await extractor_service.extract_text_from_resource(
            res_id=res_id,
            username=username,
            force_reextract=force_reextract,
            total_questions=total_questions,
            explanation_start_page=explanation_start_page
        )

        if result.get("status") == "success":
            # Update both tracking systems to COMPLETED/completed
            tracker_service.update_task_status(request_task_id, 'COMPLETED', result_data=result)
            mcq_extract_log.update_extraction_log(mcq_task_id, 'completed')
            logger.info(f"[TASK:{request_task_id}] MCQ text extraction completed successfully")
            logger.info(f"[MCQ_TASK:{mcq_task_id}] MCQ extract log updated to completed")
        else:
            # Update both tracking systems to FAILED/failed
            error_message = result.get("message", "Unknown error occurred")
            tracker_service.update_task_status(request_task_id, 'FAILED', error_message)
            mcq_extract_log.update_extraction_log(mcq_task_id, 'failed')
            logger.error(f"[TASK:{request_task_id}] MCQ text extraction failed: {error_message}")
            logger.error(f"[MCQ_TASK:{mcq_task_id}] MCQ extract log updated to failed")

    except Exception as e:
        # Update both tracking systems to FAILED/failed
        error_message = str(e)
        tracker_service.update_task_status(request_task_id, 'FAILED', error_message)
        mcq_extract_log.update_extraction_log(mcq_task_id, 'failed')
        logger.error(f"[TASK:{request_task_id}] Error in background MCQ text extraction: {error_message}")
        logger.error(f"[MCQ_TASK:{mcq_task_id}] Error in background MCQ text extraction: {error_message}")
        logger.error(traceback.format_exc())


async def process_mcq_translation_background(request_task_id: str, pdf_content: bytes, filename: str,
                                           username: str, total_questions: int, source_language: str,
                                           destination_language: str):
    """
    Background task function for MCQ translation.

    Args:
        request_task_id: Request tracker task ID for tracking
        pdf_content: PDF file content as bytes
        filename: Original filename of the PDF
        username: Username
        total_questions: Total number of questions
        source_language: Source language of the content
        destination_language: Target language to translate to
    """
    # Import services inside the background task to avoid blocking main thread
    from services.request_tracker_service import RequestTrackerService
    from agents.mcq_translation_log import MCQTranslationLog

    tracker_service = RequestTrackerService()
    translation_log = MCQTranslationLog()

    try:
        # Add a small delay to ensure the API response is sent first
        import asyncio
        await asyncio.sleep(0.1)

        # Update tracking system to IN_PROGRESS
        tracker_service.update_task_status(request_task_id, 'IN_PROGRESS')

        logger.info(f"[TASK:{request_task_id}] Starting background MCQ translation for file {filename}")
        logger.info(f"[TASK:{request_task_id}] Task status updated to IN_PROGRESS")

        # Generate translation ID for this translation
        import uuid
        translation_id = str(uuid.uuid4())

        # Create MCQ translation log entry
        log_created = translation_log.create_translation_log(
            username=username,
            translation_id=translation_id,
            original_filename=filename,
            source_language=source_language,
            destination_language=destination_language,
            total_questions=total_questions
        )

        if not log_created:
            logger.error(f"[TASK:{request_task_id}] Failed to create MCQ translation log")
            tracker_service.update_task_status(request_task_id, 'FAILED', 'Failed to create translation log')
            return

        logger.info(f"[TASK:{request_task_id}] Created MCQ translation log with translation_id: {translation_id}")

        # Now import and initialize the heavy service
        from services.mcq_translator_file_service import MCQTranslatorFileService
        translator_service = MCQTranslatorFileService()

        # Call the service to translate text from file with the request_id and translation_id
        result = await translator_service.translate_text_from_file(
            pdf_file_content=pdf_content,
            filename=filename,
            username=username,
            total_questions=total_questions,
            source_language=source_language,
            destination_language=destination_language,
            request_id=request_task_id,  # Pass the request_id to avoid duplicate generation
            translation_id=translation_id  # Pass the translation_id to avoid duplicate generation
        )

        # Check if translation was successful
        if result.get("status") == "success":
            # Update MCQ translation log to completed
            translated_s3_path = result.get("translated_s3_path")
            translation_log.update_translation_status(translation_id, 'completed', translated_s3_path)

            # Update task status to completed with result data
            result_data = {
                "translation_id": translation_id,  # Use the translation_id we created
                "filename": filename,
                "total_questions": total_questions,
                "source_language": source_language,
                "destination_language": destination_language,
                "translated_s3_path": translated_s3_path,
                "message": result.get("message", "Translation completed successfully")
            }

            tracker_service.update_task_status(request_task_id, 'COMPLETED', result_data=result_data)
            logger.info(f"[TASK:{request_task_id}] MCQ translation completed successfully")
        else:
            # Update MCQ translation log to failed
            translation_log.update_translation_status(translation_id, 'failed')

            # Update task status to failed
            error_message = result.get("message", "Translation failed")
            tracker_service.update_task_status(request_task_id, 'FAILED', error_message=error_message)
            logger.error(f"[TASK:{request_task_id}] MCQ translation failed: {error_message}")

    except Exception as e:
        # Update MCQ translation log to failed
        try:
            translation_log.update_translation_status(translation_id, 'failed')
        except:
            pass  # Ignore errors in error handling

        # Update task status to failed
        error_message = f"Translation error: {str(e)}"
        tracker_service.update_task_status(request_task_id, 'FAILED', error_message=error_message)
        logger.error(f"[TASK:{request_task_id}] MCQ translation failed with exception: {e}")
        logger.error(traceback.format_exc())


@router.post("/api/mcq-text-extractor")
async def mcq_text_extractor(request: MCQTextExtractQuery):
    import time
    start_time = time.time()

    # Get the resource ID, force_reextract flag, total_questions, and explanation_start_page
    res_id = request.resId
    username = request.username
    force_reextract = request.force_reextract
    total_questions = request.total_questions
    explanation_start_page = getattr(request, 'explanation_start_page', None)

    logger.info(f"[TIMING] MCQ text extractor API called at {start_time}")

    # Initialize logging services
    from agents.mcq_extract_log import MCQExtractLog
    from services.request_tracker_service import RequestTrackerService

    mcq_extract_log = MCQExtractLog()
    tracker_service = RequestTrackerService()

    try:
        # Create MCQ extract log entry
        mcq_task_id = mcq_extract_log.create_extraction_log(res_id=res_id, username=username)
        if not mcq_task_id:
            logger.error("Failed to create MCQ extraction log")
            return {"status": "error", "message": "Failed to create MCQ extraction log"}

        # Create request tracker entry
        request_task_id = tracker_service.create_task(task_type="mcq_text_extraction")
        if not request_task_id:
            logger.error("Failed to create request tracker task")
            return {"status": "error", "message": "Failed to create request tracker task"}

        logger.info(f"Created MCQ extract log with task_id: {mcq_task_id}")
        logger.info(f"Created request tracker with task_id: {request_task_id}")

    except Exception as e:
        logger.error(f"Error creating logging entries: {e}")
        return {"status": "error", "message": f"Error creating logging entries: {str(e)}"}

    # Start the background task using threading to ensure it doesn't block the response
    import threading
    import asyncio

    background_start_time = time.time()

    def run_background_task():
        """Run the background task in a separate thread with its own event loop."""
        try:
            # Create a new event loop for this thread
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)

            # Run the background task with both task IDs
            loop.run_until_complete(process_mcq_text_extraction_background(
                request_task_id, mcq_task_id, res_id, username, force_reextract, total_questions, explanation_start_page
            ))

        except Exception as e:
            logger.error(f"Background task {request_task_id} failed with exception: {e}")
            # Update both tracking systems to failed status
            try:
                tracker_service.update_task_status(request_task_id, 'FAILED', str(e))
                mcq_extract_log.update_extraction_log(mcq_task_id, 'failed')
            except:
                pass  # Ignore errors in error handling
        finally:
            try:
                loop.close()
            except:
                pass

    # Start the background thread
    background_thread = threading.Thread(target=run_background_task, daemon=True)
    background_thread.start()

    background_creation_time = time.time()
    logger.info(f"[TIMING] Background thread started in {background_creation_time - background_start_time:.3f} seconds")

    logger.info(f"[TASK:{request_task_id}] Background task started in separate thread")

    response_time = time.time()
    total_time = response_time - start_time
    logger.info(f"[TIMING] Total API response time: {total_time:.3f} seconds")

    # Return both task IDs immediately
    return {
        "status": "started",
        "task_id": request_task_id,
        "mcq_task_id": mcq_task_id,
        "message": "MCQ text extraction started in background",
        "response_time": f"{total_time:.3f}s"
    }

@router.post("/api/mcq-text-extractor/status")
async def get_mcq_text_extraction_status(request: TaskStatusRequest):
    """
    Get the status of an MCQ text extraction task.

    Args:
        request: TaskStatusRequest containing the task_id

    Returns:
        dict: Task status information
    """
    try:
        # Import the request tracker service
        from services.request_tracker_service import RequestTrackerService

        # Create an instance of the service
        tracker_service = RequestTrackerService()

        # Get task status
        status_info = tracker_service.get_task_status(request.task_id)

        if status_info.get("status") == "not_found":
            return {
                "status": "not_found",
                "message": f"Task {request.task_id} not found"
            }
        elif status_info.get("status") == "error":
            return status_info

        # If task is completed, get the extraction results and clean up
        if status_info.get("status") == "COMPLETED":
            # Get the result data from error_message field (where we stored it)
            result_data = None
            if status_info.get("error_message"):
                try:
                    import json
                    result_data = json.loads(status_info.get("error_message"))
                except:
                    # If not JSON, treat as plain text
                    result_data = {"message": status_info.get("error_message")}

            result = {
                "status": "COMPLETED",
                "task_id": request.task_id,
                "message": "MCQ text extraction completed successfully",
                "completed_at": status_info.get("last_updated"),
                "result": result_data
            }

            # Clean up the task record
            tracker_service.delete_task(request.task_id)

            return result

        # Return current status
        return {
            "status": status_info.get("status"),
            "task_id": request.task_id,
            "created_at": status_info.get("date_created"),
            "last_updated": status_info.get("last_updated"),
            "error": status_info.get("error", False),
            "error_message": status_info.get("error_message")
        }

    except Exception as e:
        logger.error(f"Error getting task status for {request.task_id}: {str(e)}")
        logger.error(traceback.format_exc())

        return {
            "status": "error",
            "message": str(e)
        }


@router.post("/api/mcq-text-extractor/mcq-status")
async def get_mcq_extraction_log_status(request: TaskStatusRequest):
    """
    Get the status of an MCQ text extraction task from MCQ extract log.

    Args:
        request: TaskStatusRequest containing the mcq_task_id

    Returns:
        dict: MCQ extraction log status information
    """
    try:
        # Import the MCQ extract log service
        from agents.mcq_extract_log import MCQExtractLog

        # Create an instance of the service
        mcq_extract_log = MCQExtractLog()

        # Get MCQ extraction status
        status_info = mcq_extract_log.get_extraction_status(request.task_id)

        return status_info

    except Exception as e:
        logger.error(f"Error getting MCQ extraction log status for {request.task_id}: {str(e)}")
        logger.error(traceback.format_exc())

        return {
            "status": "error",
            "message": str(e)
        }


@router.post("/api/mcq-translator-file/status")
async def get_mcq_translation_status(request: TaskStatusRequest):
    """
    Get the status of an MCQ translation task.

    Args:
        request: TaskStatusRequest containing the task_id

    Returns:
        dict: Task status information
    """
    try:
        # Import the request tracker service
        from services.request_tracker_service import RequestTrackerService

        # Create an instance of the service
        tracker_service = RequestTrackerService()

        # Get task status
        status_info = tracker_service.get_task_status(request.task_id)

        if status_info.get("status") == "not_found":
            return {
                "status": "not_found",
                "message": f"Task {request.task_id} not found"
            }
        elif status_info.get("status") == "error":
            return status_info

        # If task is completed, get the translation results and clean up
        if status_info.get("status") == "COMPLETED":
            # Get the result data from error_message field (where we stored it)
            result_data = None
            if status_info.get("error_message"):
                try:
                    import json
                    result_data = json.loads(status_info.get("error_message"))
                except:
                    # If not JSON, treat as plain text
                    result_data = {"message": status_info.get("error_message")}

            result = {
                "status": "COMPLETED",
                "task_id": request.task_id,
                "message": "MCQ translation completed successfully",
                "completed_at": status_info.get("last_updated"),
                "result": result_data
            }

            # Clean up the task record
            tracker_service.delete_task(request.task_id)

            return result

        # If task is failed, return error information and clean up
        elif status_info.get("status") == "FAILED":
            result = {
                "status": "FAILED",
                "task_id": request.task_id,
                "message": status_info.get("error_message", "Translation failed"),
                "failed_at": status_info.get("last_updated")
            }

            # Clean up the task record
            tracker_service.delete_task(request.task_id)

            return result

        # Task is still in progress
        else:
            return {
                "status": status_info.get("status"),
                "task_id": request.task_id,
                "message": "Translation in progress...",
                "started_at": status_info.get("date_created"),
                "last_updated": status_info.get("last_updated")
            }

    except Exception as e:
        logger.error(f"Error getting MCQ translation status: {e}")
        logger.error(traceback.format_exc())
        return {"status": "error", "message": str(e)}


@router.post("/api/mcq-translator-file/translation-log-status")
async def get_mcq_translation_log_status(request: TaskStatusRequest):
    """
    Get the status of an MCQ translation task from the translation log.

    Args:
        request: TaskStatusRequest containing the translation_id

    Returns:
        dict: Translation log status information
    """
    try:
        # Import the MCQ translation log service
        from agents.mcq_translation_log import MCQTranslationLog

        # Create an instance of the service
        translation_log = MCQTranslationLog()

        # Get translation status
        status_info = translation_log.get_translation_status(request.task_id)

        return status_info

    except Exception as e:
        logger.error(f"Error getting MCQ translation log status for {request.task_id}: {str(e)}")
        logger.error(traceback.format_exc())

        return {
            "status": "error",
            "message": str(e)
        }


@router.get("/api/get-mcq-text/{chapter_id}/{res_id}")
async def get_mcq_text(chapter_id: str, res_id: str, login_check=Depends(require_login), role_check=Depends(require_roles(["ROLE_GPT_MANAGER"]))):
    """Get extracted MCQ text content"""

    try:
        # Import the service
        from services.mcq_text_extractor_service import MCQTextExtractorService

        # Create an instance of the service
        service = MCQTextExtractorService()

        # Get the extracted text
        result = await service.get_extracted_text(chapter_id=chapter_id, res_id=res_id)

        return result
    except Exception as e:
        logger.error(f"Error getting MCQ text for chapter {chapter_id}, resource {res_id}: {e}")
        return {"status": "error", "message": str(e)}


@router.get("/api/download-mcq-text/{chapter_id}/{res_id}")
async def download_mcq_text(
    chapter_id: str,
    res_id: str,
    role_check=Depends(require_roles(["ROLE_GPT_MANAGER"]))
):
    """Download extracted MCQ text file"""

    try:
        # Import the service
        from services.mcq_text_extractor_service import MCQTextExtractorService

        # Create an instance of the service
        service = MCQTextExtractorService()

        # Get the extracted text
        result = await service.get_extracted_text(chapter_id=chapter_id, res_id=res_id)

        if result["status"] != "success":
            return result

        # Return the file as a download
        from fastapi.responses import Response

        content = result["content"]
        filename = f"mcq_text_{chapter_id}_{res_id}.txt"

        return Response(
            content=content.encode('utf-8'),
            media_type="text/plain",
            headers={"Content-Disposition": f"attachment; filename={filename}"}
        )

    except Exception as e:
        logger.error(f"Error downloading MCQ text for chapter {chapter_id}, resource {res_id}: {e}")
        return {"status": "error", "message": str(e)}

@router.post("/api/processAdminPDFVector")
async def process_pdf_vector_admin(request: Request):
    """
    Endpoint to process a PDF file and convert it into a vector.
    It accepts a POST request with a form data containing the PDF file.

    Args:
        request (Request): The request object containing the form data.

    Returns:
        dict: A message indicating the success of the operation.
    """
    try:
        form = await request.form()
        file = form["file"].file.read()  # get file as bytes
        # Check if "filename" parameter is provided and has a value
        filename_param = form.get("filename")
        if filename_param:
            filename = filename_param  # Use provided filename
        else:
            filename = form["file"].filename  # Get filename from the uploaded file

        # Check if "filename" has an extension
        if '.' in filename:
            file_name = os.path.splitext(filename)[0]
        else:
            file_name = filename

        user_type = "admin"
        split_values = file_name.split('_')
        count_values = len(split_values)
        index = INDEX_NAME_USER
        if count_values == 3:
            index = split_values[0]
            file_name = split_values[1] + "_" + split_values[2]

        logger.info(f"Processing PDF For Admin: {file_name}")
        # Extract text from the file data
        process_pdf(file_name, file, index, user_type)
        logger.info(f"PDF processed successfully for admin: {file_name}")

    except Exception as e:
        logger.error(f"Error processing PDF: {e}")
        raise HTTPException(status_code=500, detail=str(e))

    return {"message": "PDF processed successfully"}


@router.post("/api/processPDFVectorNew")
async def process_pdf_vector_new(request: ProcessPDFQuery):
    """
    Endpoint to process a PDF file and convert it into a vector.
    It accepts a POST request.

    Args:
        request (ProcessPDFQuery): The request object containing the params.

    Returns:
        dict: A message indicating the success of the operation.
    """
    try:
        namespace = request.namespace
        original_namespace = namespace  # Store original namespace before any modifications
        file_path = request.filePath
        index = INDEX_NAME_USER
        user_type = "user"
        file_name = os.path.basename(file_path)

        split_values = namespace.split('_')
        count_values = len(split_values)
        if count_values == 3:
            index = split_values[0]
            namespace = split_values[1] + "_" + split_values[2]

        is_pdf_exists = get_namespaces(namespace, index, user_type)
        if not is_pdf_exists:
            logger.info("Processing PDF to vector")
            # Extract res_id from namespace if available
            res_id = None
            if '_' in namespace:
                try:
                    res_id = namespace.split('_')[-1]
                    logger.info(f"Extracted resource ID from namespace: {res_id}")
                except:
                    logger.warning("Could not extract resource ID from namespace")

            # Process the PDF
            processing_result = await process_pdf_new(file_path, namespace, index, user_type, res_id)

            # Update vector_stored field if processing was successful and res_id is available
            if processing_result and res_id:
                from utils.db_utils import update_vector_stored
                logger.info(f"Updating vector_stored field for resource ID: {res_id} with namespace: {original_namespace}")
                update_success = update_vector_stored(res_id, original_namespace)
                if update_success:
                    logger.info(f"Successfully updated vector_stored field for resource ID: {res_id}")
                else:
                    logger.warning(f"Failed to update vector_stored field for resource ID: {res_id}")

        logger.info(f"PDF processed successfully: {file_name}")

    except Exception as e:
        logger.error(f"Error processing PDF: {e}")
        raise HTTPException(status_code=500, detail=str(e))

    return {"message": "PDF processed successfully"}


@router.post("/api/uploadDrivePdf")
async def upload_pdf_drive(request: ProcessPDFQuery):
    """
    Endpoint to process a PDF file and convert it into a vector.
    It accepts a POST request.

    Args:
        request (ProcessPDFQuery): The request object containing the params.

    Returns:
        dict: A message indicating the success of the operation.
    """
    try:
        namespace = "drive_"+request.namespace
        file_path = request.filePath
        index = INDEX_NAME_DRIVE
        user_type = "drive"
        file_name = os.path.basename(file_path)
        logger.info("Processing PDF to vector")
        process_pdf_new(file_path, namespace, index, user_type)

        logger.info(f"PDF processed successfully: {file_name}")

    except Exception as e:
        logger.error(f"Error processing PDF: {e}")
        raise HTTPException(status_code=500, detail=str(e))

    return {"message": "PDF processed successfully"}


@router.get("/api/checkPDFExists")
async def check_pdf_exists(namespace: str):
    """
    Endpoint to check if a PDF exists in the namespace.

    It accepts a GET request with a URL query parameter `namespace`.

    Args:
        namespace (str): The namespace in which to check for the PDF.

    Returns:
        dict: A dictionary containing the status code, any error message,
        a response message indicating whether the PDF exists, a boolean
        indicating the existence of the PDF, and the namespace.
    """
    is_exist = False
    temp_ns = namespace
    logger.info(f"Checking if PDF exists in namespace: {namespace}")
    try:
        user_type = "user"
        split_values = namespace.split('_')
        count_values = len(split_values)
        index = INDEX_NAME_USER
        if count_values == 3:
            index = split_values[0]
            namespace = split_values[1] + "_" + split_values[2]
        is_pdf_exists = get_namespaces(namespace, index, user_type)
        if is_pdf_exists:
            is_exist = True
            res_message = 'pdf already exists'
        else:
            is_exist = False
            res_message = 'pdf does not exists'

        logger.info(f"PDF exists: {is_pdf_exists}")

        return {
            'statusCode': 200,
            'err': None,
            'resMessage': res_message,
            'isExist': is_exist,
            'namespace': temp_ns,
        }
    except Exception as error:
        logger.error(f"Error checking if PDF exists: {error}")
        return {
            'statusCode': 200,
            'err': str(error),
            'resMessage': str(error),
            'isExist': is_exist,
            'namespace': temp_ns,
        }


@router.get("/api/checkDrivePdf")
async def check_drive_pdf(namespace: str):
    """
    Endpoint to check if a PDF exists in the namespace.

    It accepts a GET request with a URL query parameter `namespace`.

    Args:
        namespace (str): The namespace in which to check for the PDF.

    Returns:
        dict: A dictionary containing the status code, any error message,
        a response message indicating whether the PDF exists, a boolean
        indicating the existence of the PDF, and the namespace.
    """
    is_exist = False
    drive_ns = "drive_"+namespace
    temp_ns = namespace
    logger.info(f"Checking if PDF exists in namespace: {drive_ns}")
    try:
        is_pdf_exists = check_namespace_in_redis(drive_ns)
        if is_pdf_exists:
            is_exist = True
            res_message = 'pdf already exists'
        else:
            is_exist = False
            res_message = 'pdf does not exists'

        logger.info(f"PDF exists: {is_pdf_exists}")

        return {
            'statusCode': 200,
            'err': None,
            'resMessage': res_message,
            'isExist': is_exist,
            'namespace': temp_ns,
        }
    except Exception as error:
        logger.error(f"Error checking if PDF exists: {error}")
        return {
            'statusCode': 200,
            'err': str(error),
            'resMessage': str(error),
            'isExist': is_exist,
            'namespace': temp_ns,
        }


@router.get("/api/checkAdminPDFExists")
async def check_pdf_exists(namespace: str):
    """
    Endpoint to check if a PDF exists in the namespace.

    It accepts a GET request with a URL query parameter `namespace`.

    Args:
        namespace (str): The namespace in which to check for the PDF.

    Returns:
        dict: A dictionary containing the status code, any error message,
        a response message indicating whether the PDF exists, a boolean
        indicating the existence of the PDF, and the namespace.
    """
    is_exist = False
    temp_ns = namespace
    logger.info(f"Checking if PDF exists in namespace: {namespace}")
    try:
        user_type = "admin"
        split_values = namespace.split('_')
        count_values = len(split_values)
        index = INDEX_NAME_ADMIN
        if count_values == 3:
            index = split_values[0]
            namespace = split_values[1] + "_" + split_values[2]
        is_pdf_exists = get_namespaces(namespace, index, user_type)
        if is_pdf_exists:
            is_exist = True
            res_message = 'pdf already exists'
        else:
            is_exist = False
            res_message = 'pdf does not exists'

        logger.info(f"PDF exists: {is_pdf_exists}")

        return {
            'statusCode': 200,
            'err': None,
            'resMessage': res_message,
            'isExist': is_exist,
            'namespace': temp_ns,
        }
    except Exception as error:
        logger.error(f"Error checking if PDF exists: {error}")
        return {
            'statusCode': 200,
            'err': str(error),
            'resMessage': str(error),
            'isExist': is_exist,
            'namespace': temp_ns,
        }


@router.post("/api/retrieveData")
async def retrieve_data(request: Request):
    """
    Endpoint to retrieve data based on a given query.
    It accepts a POST request with a JSON body containing the query parameters.

    Args:
        request (Request): The request object containing the query parameters.

    Returns:
        dict: The response from the data retrieval operation.
    """
    try:
        user_type = "user"
        body = await request.json()
        # Retrieve response
        prompt = body.get('query')
        namespace = body.get('namespace')
        res_type = body.get('resType')
        chat_history = body.get('chatHistory', None)
        chat_history_app = body.get('chatHistoryApp', None)
        custom_prompt = body.get('customPrompt', None)
        que_type = body.get('type', None)
        mcq = body.get('mcq', None)
        for_book = False

        if mcq:
            response = generate_mcq_res(prompt, chat_history, que_type)
        else:
            response = generate_response(prompt, namespace, res_type, chat_history, chat_history_app, user_type, custom_prompt, for_book)

    except Exception as e:
        logger.error(f"Error retrieving data: {e}")
        raise HTTPException(status_code=500, detail=str(e))

    logger.info("Data retrieved successfully")
    return response


@router.post("/api/retrieveDataForBook")
async def retrieve_data(request: Request):
    """
    Endpoint to retrieve data based on a given query.
    It accepts a POST request with a JSON body containing the query parameters.

    Args:
        request (Request): The request object containing the query parameters.

    Returns:
        dict: The response from the data retrieval operation.
    """
    try:
        user_type = "user"
        body = await request.json()
        # Retrieve response
        prompt = body.get('query')
        namespace = body.get('namespace')
        res_type = body.get('resType')
        chat_history = body.get('chatHistory', None)
        chat_history_app = body.get('chatHistoryApp', None)
        custom_prompt = body.get('customPrompt', None)
        que_type = body.get('type', None)
        mcq = body.get('mcq', None)
        for_book = True
        if mcq:
            response = generate_mcq_res(prompt, chat_history, que_type)
        else:
            response = generate_response(prompt, namespace, res_type, chat_history, chat_history_app, user_type, custom_prompt, for_book)

    except Exception as e:
        logger.error(f"Error retrieving data: {e}")
        raise HTTPException(status_code=500, detail=str(e))

    logger.info("Data retrieved successfully")
    return response

@router.post("/api/retrieveDataAdminNew")
async def retrieve_data_admin_new(request: AdminQueryNew):
    """
    Endpoint to retrieve data for admin based on a given query.
    It accepts a POST request with a JSON body containing the query parameters.

    Args:
        request (AdminQuery): The request object containing the query parameters.

    Returns:
        dict: The response from the data retrieval operation.
    """
    logger.info(f"[DB-DEBUG] Starting retrieve_data_admin_new endpoint handler")
    logger.info(f"[DB-DEBUG] Request parameters: {request}")

    try:
        # Retrieve response
        logger.info("Retrieving data for admin")
        response = await generate_response_admin_new(request)

    except Exception as e:
        logger.error(f"Error retrieving data for admin: {e}")
        raise HTTPException(status_code=500, detail=str(e))

    logger.info("Data retrieved successfully for admin")
    return response


@router.delete("/api/deleteNs")
async def delete_ns(request: DeleteNs):
    """
    Endpoint to retrieve data for admin based on a given query.
    It accepts a POST request with a JSON body containing the query parameters.

    Args:
        request (AdminQuery): The request object containing the query parameters.

    Returns:
        dict: The response from the data retrieval operation.
    """
    try:
        print(request)
        count = request.count
        index = request.index
        usertype = request.usertype
        print(count)
        # Retrieve response
        response = delete_old_ns(count, index, usertype)

    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

    return response


@router.post("/api/delete-namespace")
async def delete_namespace_endpoint(request: DeleteNamespaceRequest):
    """
    Endpoint to delete a specific namespace from Pinecone.
    It accepts a DELETE request with a JSON body containing the namespace parameter.

    The namespace format should be like: user1_1812502_2213673
    - The part before the first underscore (user1) will be used as the index name
    - The remaining part (1812502_2213673) will be used as the actual namespace
    - Optionally, you can provide an index parameter to override the extracted index

    Args:
        request (DeleteNamespaceRequest): The request object containing the namespace and optional index parameters.

    Returns:
        dict: The response from the namespace deletion operation.
    """
    try:
        namespace = request.namespace
        provided_index = request.index

        logger.info(f"Delete namespace request received - namespace: {namespace}, provided_index: {provided_index}")

        # Extract index and actual namespace from the namespace parameter
        # Following the same pattern as in generate_response function in app/data_retriever.py
        temp_ns = namespace
        split_values = namespace.split('_')
        count_values = len(split_values)

        if count_values >= 3:
            # Extract index from the first part (e.g., "user1" from "user1_1812502_2213673")
            extracted_index = split_values[0]
            # Use the remaining parts as the actual namespace (e.g., "1812502_2213673")
            actual_namespace = "_".join(split_values[1:])
        else:
            # If the format doesn't match expected pattern, use the whole string as namespace
            extracted_index = None
            actual_namespace = namespace

        # Use provided index if available, otherwise use extracted index
        if provided_index:
            index_to_use = provided_index
            logger.info(f"Using provided index: {index_to_use}")
        elif extracted_index:
            index_to_use = extracted_index
            logger.info(f"Using extracted index: {index_to_use}")
        else:
            raise HTTPException(
                status_code=400,
                detail="Could not determine index. Please provide an index parameter or use namespace format like 'user1_1812502_2213673'"
            )

        # Determine user type based on index name
        # Following the pattern from the existing codebase
        if index_to_use.lower().startswith('user'):
            user_type = "user"
        elif index_to_use.lower() == INDEX_NAME_ADMIN.lower():
            user_type = "admin"
        elif index_to_use.lower() == INDEX_NAME_DRIVE.lower():
            user_type = "drive"
        else:
            # Default to user type for custom index names
            user_type = "user"

        logger.info(f"Deleting namespace '{actual_namespace}' from index '{index_to_use}' with user type '{user_type}'")

        # Delete the namespace
        response = delete_namespace(actual_namespace, index_to_use, user_type)

        # Add original namespace info to response
        response["original_namespace"] = temp_ns
        response["extracted_index"] = extracted_index
        response["actual_namespace"] = actual_namespace
        response["index_used"] = index_to_use
        response["user_type"] = user_type

        logger.info(f"Delete namespace operation completed: {response}")

        return response

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error in delete namespace endpoint: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/api/retrieveDataAdminText")
async def retrieve_data_admin_text(request: Request):
    try:
        logger.info("Duplicate finder api")
        # Get the form data
        form = await request.form()

        prompt = form.get("prompt", "")
        res_type = "cleanup"

        # Access the file from the form data
        uploaded_file = form["file"].file.read()  # Get file as bytes

        # Decode the file content to a string (assuming it's UTF-8 encoded)
        file_content = uploaded_file.decode("utf-8")
        logger.info("Processing txt file")

        response = text_processor(file_content, prompt, res_type)
    except Exception as e:
        print(f"Error processing text: {e}")
        raise HTTPException(status_code=500, detail=str(e))

    return {"response": response}


@router.post("/api/extension-embedding")
async def extension_chat(request: Request):
    is_ns_exists = False
    try:
        body = await request.json()
        content = body.get("content")
        namespace = body.get("tabId")
        index = INDEX_NAME_DRIVE

        namespace_exists = check_namespace_in_redis(namespace)
        if namespace_exists:
            is_ns_exists = True
            res_message = "namespace exists"
        else:
            process_ext_content(content, index, "drive", namespace)
            res_message = "webpage content stored successfully"
    except Exception as e:
        logger.error(f"Error processing content: {e}")
        raise HTTPException(status_code=500, detail=str(e))
    return {"isExists": is_ns_exists, "message": res_message}


@router.post("/api/extension-chat")
async def extension_chat(request: Request):
    """
    Endpoint to retrieve data based on a given query.
    It accepts a POST request with a JSON body containing the query parameters.

    Args:
        request (Request): The request object containing the query parameters.

    Returns:
        dict: The response from the data retrieval operation.
    """
    try:
        user_type = "drive"
        body = await request.json()
        # Retrieve response
        prompt = body.get('query')
        namespace = body.get('namespace')
        res_type = body.get('resType')
        chat_history = body.get('chatHistory', None)
        response = generate_ext_response(prompt, namespace, res_type, chat_history, user_type)

    except Exception as e:
        logger.error(f"Error retrieving data: {e}")
        raise HTTPException(status_code=500, detail=str(e))

    logger.info("Data retrieved successfully")
    return response


@router.post("/api/driveChat")
async def drive_chat(request: Request):
    """
    Endpoint to retrieve data based on a given query.
    It accepts a POST request with a JSON body containing the query parameters.

    Args:
        request (Request): The request object containing the query parameters.

    Returns:
        dict: The response from the data retrieval operation.
    """
    try:
        user_type = "drive"
        body = await request.json()
        # Retrieve response
        prompt = body.get('query')
        namespace = "drive_"+body.get('namespace')
        res_type = body.get('resType')
        chat_history = body.get('chatHistory', None)
        response = generate_ext_response(prompt, namespace, res_type, chat_history, user_type)

    except Exception as e:
        logger.error(f"Error retrieving data: {e}")
        raise HTTPException(status_code=500, detail=str(e))

    logger.info("Data retrieved successfully")
    return response


@router.post("/api/img_chat")
async def img_chat_2(request: Request):
    """
    Endpoint to retrieve data based on an uploaded image.
    It accepts a POST request with an image file.

    Args:
        request (UploadFile): The uploaded image file.

    Returns:
        dict: The description of the image from the AI model.
    """
    try:
        body = await request.json()
        image_url = body.get("imgData")
        query = body.get("query")
        res_type = body.get("resType")
        img_link = body.get("imgLink")
        text = generate_response_md(image_url, query)
        return {"answer": text.content, "resType": res_type, "imgLink": img_link}
    except Exception as e:
        logger.error(f"Error retrieving data: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/api/mcq-chat")
async def mcq_chat(request: Request):
    """
    Endpoint to retrieve data based on a given query.
    It accepts a POST request with a JSON body containing the query parameters.

    Args:
        request (Request): The request object containing the query parameters.

    Returns:
        dict: The response from the data retrieval operation.
    """
    try:
        body = await request.json()
        # Retrieve response
        prompt = body.get('query')
        temp_chat_history = body.get('chatHistory', None)
        temp_chat_history_app = body.get('chatHistoryApp', None)
        que_type = body.get('type', None)
        user_query = body.get('userQuery', None)
        has_image = body.get('hasImage', None)
        language = body.get('language', None)
        if temp_chat_history_app and isinstance(temp_chat_history_app, str):
            temp_chat_history_app = json.loads(temp_chat_history_app)
        response = generate_mcq_res(prompt, temp_chat_history, que_type, user_query, has_image, language, temp_chat_history_app)
    except Exception as e:
        logger.error(f"Error retrieving data: {e}")
        raise HTTPException(status_code=500, detail=str(e))

    logger.info("Data retrieved successfully")
    return response

@router.post("/api/modify-content")
async def tts_content_handler(request: Request):
    try:
        raw_body = await request.body()
        logger.info(f"Raw request body: {raw_body}")
        body = await request.json()
        content = body.get('content')
        language = body.get("language")

        audio_content, timepoints = process_tts(content, language)

        # Encode audio content to base64 for JSON response
        audio_base64 = base64.b64encode(audio_content).decode("utf-8")

        # Construct the response
        response_data = {
            "audioContent": audio_base64,
            "timepoints": [
                {"markName": tp.mark_name, "timeSeconds": tp.time_seconds}
                for tp in timepoints
            ],
        }

        return JSONResponse(content=response_data)
    except Exception as e:
        logger.error(f"Error while handling tts data: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/api/pdf-text-extract")
async def pdf_text_extract(extract_query: PDFTextExtractQuery = None):
    """Extract text from a PDF resource"""
    # Get the resource ID from the request body or query parameter
    res_id = None

    if extract_query:
        # If using Pydantic model
        res_id = extract_query.resId

    if not res_id:
        raise HTTPException(status_code=400, detail="Resource ID is required")

    try:
        # Import the progress tracker and PDF text extractor
        from agents.pdf_text_extractor import PDFTextExtractor
        # Initialize the PDF text extractor with the progress tracker
        extractor = PDFTextExtractor()
        # Process the extraction
        result = await extractor.process_pdf_text_extraction(res_id)
        print(result)
        return result

    except Exception as e:
        logger.error(f"Error during PDF text extraction: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return {"status": "error", "message": str(e)}


@router.get("/api/read-extracted-text")
async def read_extracted_text(res_id: str):
    """Read the extracted text for a resource"""
    try:
        # Import the PDF text extractor
        from agents.pdf_text_extractor import PDFTextExtractor

        # Initialize the PDF text extractor
        extractor = PDFTextExtractor()

        # Log the request
        logger.info(f"Reading extracted text for resource ID: {res_id}")

        # Read the extracted text
        result = await extractor.read_extracted_text(res_id)

        if result["status"] == "success":
            logger.info(f"Successfully read extracted text for resource ID {res_id} from {result['source']}")
            return {"content": result["content"], "source": result["source"]}
        else:
            logger.error(f"Failed to read extracted text: {result['message']}")
            raise HTTPException(status_code=404, detail=result["message"])
    except HTTPException:
        # Re-raise HTTP exceptions
        raise
    except Exception as e:
        # Log and convert other exceptions to HTTP 500
        logger.error(f"Error in read_extracted_text endpoint: {e}")
        import traceback
        logger.error(traceback.format_exc())
        raise HTTPException(status_code=500, detail=f"Error reading extracted text: {str(e)}")


@router.get("/solution-creation", response_class=HTMLResponse)
async def solution_creation_page(
    request: Request,
    login_check=Depends(require_login),
    role_check=Depends(require_roles(["ROLE_GPT_MANAGER"]))
):
    """
    Solution creation page for creating solutions to problems
    """
    if login_check:
        return login_check

    return templates.TemplateResponse(
        "/solution_creation.html",
        {
            "request": request,
        }
    )

@router.post("/api/create-solution")
async def create_solution_endpoint(
    request: Request,
):
    """
    Create solution using sample and question inputs
    """

    try:
        # Parse form data
        form = await request.form()

        # Debug logging
        logger.info(f"Create Solution - Form keys: {form.keys()}")
        for key in form.keys():
            logger.info(f"Create Solution - Key: {key}, Type: {type(form[key])}")

        # Get sample inputs
        sample_text = form.get("sample_text")
        sample_images = []
        for key in form.keys():
            if key.startswith("sample_image_"):
                file = form[key]
                sample_images.append(file)
                logger.info(f"Added sample image: {getattr(file, 'filename', 'unknown')}")

        # Get question inputs
        question_text = form.get("question_text")
        question_images = []
        for key in form.keys():
            if key.startswith("question_image_"):
                file = form[key]
                question_images.append(file)
                logger.info(f"Added question image: {getattr(file, 'filename', 'unknown')}")

        # Validate sample inputs
        if not sample_text and not sample_images:
            raise HTTPException(
                status_code=400,
                detail="Please provide either text or images for the sample question."
            )

        if sample_text and sample_images:
            raise HTTPException(
                status_code=400,
                detail="Please provide either text OR images for the sample question, not both."
            )

        # Validate question inputs
        if not question_text and not question_images:
            raise HTTPException(
                status_code=400,
                detail="Please provide either text or images for the question to solve."
            )

        if question_text and question_images:
            raise HTTPException(
                status_code=400,
                detail="Please provide either text OR images for the question to solve, not both."
            )

        # Create the solution
        solution = await create_solution_with_sample(
            sample_text=sample_text,
            sample_images=sample_images,
            question_text=question_text,
            question_images=question_images
        )

        # Return the results
        return JSONResponse(content={
            "solution": solution
        })

    except HTTPException as e:
        # Re-raise HTTP exceptions
        raise e
    except Exception as e:
        logger.error(f"Error creating solution: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Error creating solution: {str(e)}"
        )


@router.get("/api/get-chapters-by-book/{book_id}")
async def get_chapters_by_book(book_id: str):
    """Get all chapters for a given book ID"""
    try:
        # Import the PDF text extractor
        from agents.pdf_text_extractor import PDFTextExtractor

        # Initialize the PDF text extractor
        extractor = PDFTextExtractor()

        # Log the request
        logger.info(f"Getting chapters for book ID: {book_id}")

        # Get the chapters
        result = await extractor.get_chapters_by_book_id(book_id)
        return result

    except Exception as e:
        logger.error(f"Error getting chapters for book ID {book_id}: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return {"status": "error", "message": str(e)}


@router.post("/api/pdf-text-extract-chapter")
async def pdf_text_extract_chapter(extract_query: PDFTextExtractByChapterQuery = None):
    """Extract text from a PDF resource by chapter ID"""
    # Get the chapter ID from the request body
    chapter_id = None

    if extract_query:
        # If using Pydantic model
        chapter_id = extract_query.chapterId

    if not chapter_id:
        raise HTTPException(status_code=400, detail="Chapter ID is required")

    try:
        # Import the PDF text extractor
        from agents.pdf_text_extractor import PDFTextExtractor
        # Initialize the PDF text extractor
        extractor = PDFTextExtractor()
        # Process the extraction
        result = await extractor.process_pdf_text_extraction_by_chapter(chapter_id)
        return result

    except Exception as e:
        logger.error(f"Error during PDF text extraction for chapter ID {chapter_id}: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return {"status": "error", "message": str(e)}


@router.get("/prompt-test", response_class=HTMLResponse)
async def img_chat_with_prompt(
    request: Request,
    login_check=Depends(require_login),
    role_check=Depends(require_roles(["ROLE_GPT_MANAGER"]))
):
    """
    Solution creation page for creating solutions to problems
    """
    if login_check:
        return login_check

    return templates.TemplateResponse(
        "/prompt_test.html",
        {
            "request": request,
        }
    )


@router.post("/api/chat-img")
async def chat_with_image(request: ChatImageRequest):
    # Print the received message
    print(f"Received message: {request.message}")

    # Process the image if it exists
    image_path = None
    image_data = None
    if request.image:
        # Print the first 100 characters of the base64 string (to avoid console overflow)
        print(f"Received base64 image: {request.image[:100]}...")

        # Optionally: Save the image to disk
        try:
            # Create images directory if it doesn't exist
            os.makedirs("images", exist_ok=True)

            # Decode the base64 image
            image_data = request.image

        except Exception as e:
            print(f"Error saving image: {e}")

    task_manager = TaskManager()
    response = task_manager.get_img_response_with_prompt(request.message, image_data)
    # Return a response
    return {
        "status": "success",
        "message": "Request processed successfully",
        "response": response
    }

@router.get("/api/get-translated-mcq/{chapter_id}/{res_id}")
async def get_translated_mcq(chapter_id: str, res_id: str, login_check=Depends(require_login), role_check=Depends(require_roles(["ROLE_GPT_MANAGER"]))):
    """Get translated MCQ text content"""

    try:
        # Import the service
        from services.mcq_translator_service import MCQTranslatorService

        # Create an instance of the service
        service = MCQTranslatorService()

        # Get the translated text
        result = await service.get_translated_text(chapter_id=chapter_id, res_id=res_id)

        return result
    except Exception as e:
        logger.error(f"Error getting translated MCQ text for chapter {chapter_id}, resource {res_id}: {e}")
        return {"status": "error", "message": str(e)}


@router.post("/api/mcq-translator-file")
async def mcq_translator_file(
    pdfFile: UploadFile = File(...),
    total_questions: int = Form(...),
    source_language: str = Form(...),
    destination_language: str = Form(...),
    username: str = Form("web_user"),
):
    """Translate MCQ content from uploaded PDF file using background task processing"""
    import time
    start_time = time.time()

    logger.info(f"[TIMING] MCQ translator file API called at {start_time}")

    # Initialize request tracker service
    from services.request_tracker_service import RequestTrackerService
    tracker_service = RequestTrackerService()

    try:
        # Read the uploaded file
        pdf_content = await pdfFile.read()
        filename = pdfFile.filename

        # Create task in request tracker
        request_task_id = tracker_service.create_task("mcq_translation")

        if not request_task_id:
            return {"status": "error", "message": "Failed to create task tracker"}

        logger.info(f"[TASK:{request_task_id}] Created task for MCQ translation, file: {filename}")

        # Start the background task using threading to ensure it doesn't block the response
        import threading
        import asyncio

        def run_background_task():
            """Run the background task in a separate thread with its own event loop."""
            try:
                # Create a new event loop for this thread
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)

                # Run the background task
                loop.run_until_complete(process_mcq_translation_background(
                    request_task_id, pdf_content, filename, username, total_questions, source_language, destination_language
                ))

            except Exception as e:
                logger.error(f"Background translation task {request_task_id} failed with exception: {e}")
                # Update tracking system to failed status
                try:
                    tracker_service.update_task_status(request_task_id, 'FAILED', str(e))
                except:
                    pass  # Ignore errors in error handling
            finally:
                try:
                    loop.close()
                except:
                    pass

        # Start the background thread
        background_thread = threading.Thread(target=run_background_task, daemon=True)
        background_thread.start()

        background_creation_time = time.time()
        total_time = background_creation_time - start_time

        logger.info(f"[TIMING] Background thread started in {background_creation_time - start_time:.3f} seconds")
        logger.info(f"[TASK:{request_task_id}] Background translation task started in separate thread")
        logger.info(f"[TIMING] Total API response time: {total_time:.3f} seconds")

        # Return immediately with task ID
        return {
            "status": "started",
            "task_id": request_task_id,
            "message": "MCQ translation task started successfully",
            "response_time": f"{total_time:.3f}s"
        }

    except Exception as e:
        logger.error(f"Error starting MCQ translation task: {str(e)}")
        logger.error(traceback.format_exc())
        return {"status": "error", "message": str(e)}


@router.get("/api/download-translated-mcq/{translation_id}")
async def download_translated_mcq(
    translation_id: str,
):
    """Download translated MCQ text file"""

    try:
        # Import the service
        from services.mcq_translator_file_service import MCQTranslatorFileService

        # Create an instance of the service
        service = MCQTranslatorFileService()

        # Get the translated text
        result = await service.get_translated_text(translation_id=translation_id)

        if result["status"] != "success":
            return result

        # Return the file as a download
        from fastapi.responses import Response

        content = result["content"]
        filename = f"translated_mcq_{translation_id}.txt"

        return Response(
            content=content.encode('utf-8'),
            media_type="text/plain",
            headers={"Content-Disposition": f"attachment; filename={filename}"}
        )

    except Exception as e:
        logger.error(f"Error downloading translated MCQ text for translation ID {translation_id}: {e}")
        return {"status": "error", "message": str(e)}


@router.get("/api/get-translated-mcq-content/{translation_id}")
async def get_translated_mcq_content(
    translation_id: str,
    login_check=Depends(require_login),
    role_check=Depends(require_roles(["ROLE_GPT_MANAGER"]))
):
    """Get translated MCQ text content for display"""

    try:
        # Import the service
        from services.mcq_translator_file_service import MCQTranslatorFileService

        # Create an instance of the service
        service = MCQTranslatorFileService()

        # Get the translated text
        result = await service.get_translated_text(translation_id=translation_id)

        return result
    except Exception as e:
        logger.error(f"Error getting translated MCQ content for translation ID {translation_id}: {e}")
        return {"status": "error", "message": str(e)}


@router.get("/mcq-translator", response_class=HTMLResponse)
def mcq_translator_page(request: Request, login_check=Depends(require_login), role_check=Depends(require_roles(["ROLE_GPT_MANAGER"]))):
    """MCQ Translator page"""
    if login_check:
        return login_check
    return templates.TemplateResponse("mcq_translator.html", {"request": request})


@router.post("/api/chat-completion")
async def chat_completion_endpoint(request: PromptRequest):
    """
    Chat completion endpoint that accepts a prompt and returns the LLM response.

    Args:
        request (PromptRequest): The request object containing the prompt.

    Returns:
        dict: Response containing llm_response and input.
    """
    try:
        # Get LLM instance using the factory
        llm = llm_factory.get_llm("openai_admin", "gpt-4.1-mini", req_timeout=600)

        # Call the LLM with the prompt
        response = llm.invoke(request.prompt)

        # Extract the content from the response
        llm_response = response.content if hasattr(response, 'content') else str(response)

        return {
            "response": llm_response,
            "input": request.prompt
        }

    except Exception as e:
        logger.error(f"Error in simple LLM endpoint: {e}")
        logger.error(traceback.format_exc())
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/api/perplexity-completion")
async def perplexity_completion_endpoint(request: PromptRequest):
    """
    Perplexity completion endpoint that accepts a prompt and returns the Perplexity LLM response.

    Args:
        request (PromptRequest): The request object containing the prompt.

    Returns:
        dict: Response containing llm_response and input.
    """
    try:

        # Get Perplexity LLM instance using the factory
        llm = llm_factory.get_llm("perplexity", "sonar", req_timeout=600)

        # Call the LLM with the prompt
        response = llm.invoke(request.prompt)

        # Extract the content from the response
        llm_response = response.content if hasattr(response, 'content') else str(response)

        return {
            "response": llm_response,
            "input": request.prompt
        }

    except Exception as e:
        logger.error(f"Error in Perplexity LLM endpoint: {e}")
        logger.error(traceback.format_exc())
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/api/perplexity-completion-rest")
async def perplexity_completion_endpoint(request: PromptRequest):
    """
    Perplexity completion endpoint that accepts a prompt and returns the Perplexity LLM response.

    Args:
        request (PromptRequest): The request object containing the prompt.

    Returns:
        dict: Response containing llm_response and input.
    """
    try:
        client = OpenAI(
            api_key= config.PPLX_API_KEY,
            base_url="https://api.perplexity.ai"
        )

        response = client.chat.completions.create(
            model="sonar-pro",
            messages=[
                {"role": "user", "content": request.prompt}
            ],
        )
        return {
            "response": response.choices[0].message.content,
            "input": request.prompt
        }

    except Exception as e:
        logger.error(f"Error in Perplexity LLM endpoint: {e}")
        logger.error(traceback.format_exc())
        raise HTTPException(status_code=500, detail=str(e))
